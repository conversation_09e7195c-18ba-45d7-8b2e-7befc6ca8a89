"use client";

import { useState } from 'react';
import Link from 'next/link';
import { 
  Shield, 
  Users, 
  FileText, 
  BarChart3, 
  Award, 
  AlertTriangle,
  CheckCircle,
  Clock,
  TrendingUp,
  Bell,
  Settings,
  LogOut
} from 'lucide-react';

export default function Dashboard() {
  const [activeTab, setActiveTab] = useState('overview');

  const stats = [
    {
      title: 'إجمالي الموظفين',
      value: '1,247',
      change: '+12%',
      icon: Users,
      color: 'blue'
    },
    {
      title: 'معدل الوعي الأمني',
      value: '87%',
      change: '+5%',
      icon: Shield,
      color: 'green'
    },
    {
      title: 'السياسات المكتملة',
      value: '23/30',
      change: '+3',
      icon: FileText,
      color: 'purple'
    },
    {
      title: 'مستوى الامتثال',
      value: '92%',
      change: '+2%',
      icon: Award,
      color: 'orange'
    }
  ];

  const recentActivities = [
    {
      type: 'training',
      message: 'أحمد محمد أكمل دورة "أساسيات الأمن السيبراني"',
      time: 'منذ 5 دقائق',
      icon: CheckCircle,
      color: 'green'
    },
    {
      type: 'policy',
      message: 'تم تحديث سياسة كلمات المرور',
      time: 'منذ 30 دقيقة',
      icon: FileText,
      color: 'blue'
    },
    {
      type: 'alert',
      message: 'تحذير: انخفاض في نتائج اختبار التصيد',
      time: 'منذ ساعة',
      icon: AlertTriangle,
      color: 'red'
    },
    {
      type: 'compliance',
      message: 'تم إنجاز متطلبات ISO 27001 - القسم A.8',
      time: 'منذ 2 ساعة',
      icon: Award,
      color: 'purple'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Shield className="h-8 w-8 text-blue-600 ml-2" />
              <h1 className="text-2xl font-bold text-gray-900 mr-3">CyberGuard Pro</h1>
            </div>
            
            <div className="flex items-center space-x-4">
              <button className="p-2 text-gray-400 hover:text-gray-600">
                <Bell className="h-6 w-6" />
              </button>
              <button className="p-2 text-gray-400 hover:text-gray-600">
                <Settings className="h-6 w-6" />
              </button>
              <div className="flex items-center space-x-3">
                <div className="text-right">
                  <p className="text-sm font-medium text-gray-900">أحمد المدير</p>
                  <p className="text-xs text-gray-500">مدير الأمن السيبراني</p>
                </div>
                <div className="h-8 w-8 bg-blue-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-medium">أ</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Navigation Tabs */}
        <div className="mb-8">
          <nav className="flex space-x-8">
            {[
              { id: 'overview', label: 'نظرة عامة', icon: BarChart3 },
              { id: 'awareness', label: 'الثقافة الأمنية', icon: Users },
              { id: 'compliance', label: 'الامتثال', icon: FileText },
              { id: 'reports', label: 'التقارير', icon: TrendingUp }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                  activeTab === tab.id
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
                }`}
              >
                <tab.icon className="h-5 w-5 ml-2" />
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat, index) => (
            <div key={index} className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className={`p-2 rounded-lg bg-${stat.color}-100`}>
                  <stat.icon className={`h-6 w-6 text-${stat.color}-600`} />
                </div>
                <div className="mr-4">
                  <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                  <div className="flex items-center">
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                    <span className={`mr-2 text-sm font-medium text-${stat.color}-600`}>
                      {stat.change}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content Area */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow">
              <div className="p-6 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">
                  مؤشرات الأداء الرئيسية
                </h3>
              </div>
              <div className="p-6">
                <div className="space-y-6">
                  {/* Progress Bars */}
                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium text-gray-700">
                        إكمال التدريب الأمني
                      </span>
                      <span className="text-sm text-gray-500">87%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-blue-600 h-2 rounded-full" style={{ width: '87%' }}></div>
                    </div>
                  </div>

                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium text-gray-700">
                        اختبارات التصيد المجتازة
                      </span>
                      <span className="text-sm text-gray-500">73%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-green-600 h-2 rounded-full" style={{ width: '73%' }}></div>
                    </div>
                  </div>

                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium text-gray-700">
                        تطبيق السياسات
                      </span>
                      <span className="text-sm text-gray-500">92%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-purple-600 h-2 rounded-full" style={{ width: '92%' }}></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Recent Activities */}
            <div className="bg-white rounded-lg shadow">
              <div className="p-6 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">
                  الأنشطة الأخيرة
                </h3>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  {recentActivities.map((activity, index) => (
                    <div key={index} className="flex items-start">
                      <div className={`p-1 rounded-full bg-${activity.color}-100 ml-3`}>
                        <activity.icon className={`h-4 w-4 text-${activity.color}-600`} />
                      </div>
                      <div className="flex-1">
                        <p className="text-sm text-gray-900">{activity.message}</p>
                        <p className="text-xs text-gray-500 mt-1">{activity.time}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="bg-white rounded-lg shadow">
              <div className="p-6 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">
                  إجراءات سريعة
                </h3>
              </div>
              <div className="p-6">
                <div className="space-y-3">
                  <Link 
                    href="/awareness/new-training"
                    className="block w-full text-center bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    إنشاء تدريب جديد
                  </Link>
                  <Link 
                    href="/compliance/new-policy"
                    className="block w-full text-center border border-blue-600 text-blue-600 py-2 px-4 rounded-lg hover:bg-blue-50 transition-colors"
                  >
                    إضافة سياسة
                  </Link>
                  <Link 
                    href="/reports/generate"
                    className="block w-full text-center bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700 transition-colors"
                  >
                    إنشاء تقرير
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
