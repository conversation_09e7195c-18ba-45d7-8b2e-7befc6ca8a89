"use client";

import { useState } from 'react';
import Link from 'next/link';
import { 
  Shield, 
  Play, 
  BookOpen, 
  Trophy, 
  Target,
  Clock,
  Users,
  Star,
  CheckCircle,
  ArrowRight,
  Mail,
  Lock,
  Wifi,
  Smartphone
} from 'lucide-react';

export default function AwarenessPage() {
  const [activeCategory, setActiveCategory] = useState('all');

  const trainingModules = [
    {
      id: 1,
      title: 'أساسيات الأمن السيبراني',
      description: 'تعلم المفاهيم الأساسية لحماية المعلومات والأنظمة',
      duration: '45 دقيقة',
      level: 'مبتدئ',
      completed: 87,
      total: 100,
      category: 'basics',
      icon: Shield,
      color: 'blue'
    },
    {
      id: 2,
      title: 'التعرف على رسائل التصيد',
      description: 'كيفية اكتشاف وتجنب رسائل البريد الإلكتروني المشبوهة',
      duration: '30 دقيقة',
      level: 'متوسط',
      completed: 73,
      total: 100,
      category: 'phishing',
      icon: Mail,
      color: 'red'
    },
    {
      id: 3,
      title: 'أمان كلمات المرور',
      description: 'إنشاء واستخدام كلمات مرور قوية وآمنة',
      duration: '25 دقيقة',
      level: 'مبتدئ',
      completed: 92,
      total: 100,
      category: 'passwords',
      icon: Lock,
      color: 'green'
    },
    {
      id: 4,
      title: 'أمان الشبكات اللاسلكية',
      description: 'حماية الاتصالات عبر الشبكات اللاسلكية',
      duration: '35 دقيقة',
      level: 'متقدم',
      completed: 45,
      total: 100,
      category: 'network',
      icon: Wifi,
      color: 'purple'
    },
    {
      id: 5,
      title: 'أمان الأجهزة المحمولة',
      description: 'حماية الهواتف الذكية والأجهزة اللوحية',
      duration: '40 دقيقة',
      level: 'متوسط',
      completed: 68,
      total: 100,
      category: 'mobile',
      icon: Smartphone,
      color: 'orange'
    }
  ];

  const achievements = [
    {
      title: 'خبير الأمان',
      description: 'أكمل 10 دورات تدريبية',
      icon: Trophy,
      earned: true,
      color: 'yellow'
    },
    {
      title: 'صياد التصيد',
      description: 'اجتاز جميع اختبارات التصيد',
      icon: Target,
      earned: true,
      color: 'red'
    },
    {
      title: 'حارس كلمات المرور',
      description: 'حقق 100% في اختبار كلمات المرور',
      icon: Lock,
      earned: false,
      color: 'blue'
    },
    {
      title: 'المتعلم المستمر',
      description: 'تدرب لمدة 30 يوم متتالي',
      icon: BookOpen,
      earned: false,
      color: 'green'
    }
  ];

  const categories = [
    { id: 'all', label: 'جميع الدورات', count: 5 },
    { id: 'basics', label: 'الأساسيات', count: 1 },
    { id: 'phishing', label: 'التصيد', count: 1 },
    { id: 'passwords', label: 'كلمات المرور', count: 1 },
    { id: 'network', label: 'الشبكات', count: 1 },
    { id: 'mobile', label: 'الأجهزة المحمولة', count: 1 }
  ];

  const filteredModules = activeCategory === 'all' 
    ? trainingModules 
    : trainingModules.filter(module => module.category === activeCategory);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/dashboard" className="flex items-center">
                <Shield className="h-8 w-8 text-blue-600 ml-2" />
                <h1 className="text-2xl font-bold text-gray-900 mr-3">CyberGuard Pro</h1>
              </Link>
            </div>
            
            <nav className="flex items-center space-x-6">
              <Link href="/dashboard" className="text-gray-600 hover:text-gray-900">
                لوحة التحكم
              </Link>
              <Link href="/awareness" className="text-blue-600 font-medium">
                الثقافة الأمنية
              </Link>
              <Link href="/compliance" className="text-gray-600 hover:text-gray-900">
                الامتثال
              </Link>
            </nav>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-2">
            وحدة الثقافة الأمنية
          </h2>
          <p className="text-lg text-gray-600">
            طور مهاراتك في الأمن السيبراني من خلال دورات تفاعلية ومحتوى متخصص
          </p>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <BookOpen className="h-6 w-6 text-blue-600" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">الدورات المكتملة</p>
                <p className="text-2xl font-bold text-gray-900">12</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <Trophy className="h-6 w-6 text-green-600" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">الشارات المكتسبة</p>
                <p className="text-2xl font-bold text-gray-900">8</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Clock className="h-6 w-6 text-purple-600" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">ساعات التدريب</p>
                <p className="text-2xl font-bold text-gray-900">24</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-orange-100 rounded-lg">
                <Star className="h-6 w-6 text-orange-600" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">النقاط المكتسبة</p>
                <p className="text-2xl font-bold text-gray-900">1,250</p>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar - Categories */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow p-6 mb-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                فئات التدريب
              </h3>
              <div className="space-y-2">
                {categories.map((category) => (
                  <button
                    key={category.id}
                    onClick={() => setActiveCategory(category.id)}
                    className={`w-full text-right px-3 py-2 rounded-lg transition-colors ${
                      activeCategory === category.id
                        ? 'bg-blue-100 text-blue-700'
                        : 'text-gray-600 hover:bg-gray-100'
                    }`}
                  >
                    <div className="flex justify-between items-center">
                      <span className="text-sm">{category.count}</span>
                      <span>{category.label}</span>
                    </div>
                  </button>
                ))}
              </div>
            </div>

            {/* Achievements */}
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                الإنجازات
              </h3>
              <div className="space-y-3">
                {achievements.map((achievement, index) => (
                  <div 
                    key={index}
                    className={`flex items-center p-3 rounded-lg ${
                      achievement.earned ? 'bg-yellow-50 border border-yellow-200' : 'bg-gray-50'
                    }`}
                  >
                    <div className={`p-2 rounded-lg ${
                      achievement.earned 
                        ? `bg-${achievement.color}-100` 
                        : 'bg-gray-200'
                    }`}>
                      <achievement.icon className={`h-5 w-5 ${
                        achievement.earned 
                          ? `text-${achievement.color}-600` 
                          : 'text-gray-400'
                      }`} />
                    </div>
                    <div className="mr-3">
                      <p className={`text-sm font-medium ${
                        achievement.earned ? 'text-gray-900' : 'text-gray-500'
                      }`}>
                        {achievement.title}
                      </p>
                      <p className="text-xs text-gray-500">
                        {achievement.description}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Main Content - Training Modules */}
          <div className="lg:col-span-3">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {filteredModules.map((module) => (
                <div key={module.id} className="bg-white rounded-lg shadow hover:shadow-lg transition-shadow">
                  <div className="p-6">
                    <div className="flex items-center mb-4">
                      <div className={`p-2 bg-${module.color}-100 rounded-lg`}>
                        <module.icon className={`h-6 w-6 text-${module.color}-600`} />
                      </div>
                      <div className="mr-4 flex-1">
                        <h4 className="text-lg font-medium text-gray-900">
                          {module.title}
                        </h4>
                        <div className="flex items-center text-sm text-gray-500 mt-1">
                          <Clock className="h-4 w-4 ml-1" />
                          <span>{module.duration}</span>
                          <span className="mx-2">•</span>
                          <span>{module.level}</span>
                        </div>
                      </div>
                    </div>

                    <p className="text-gray-600 mb-4">
                      {module.description}
                    </p>

                    {/* Progress Bar */}
                    <div className="mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm font-medium text-gray-700">
                          التقدم
                        </span>
                        <span className="text-sm text-gray-500">
                          {module.completed}%
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className={`bg-${module.color}-600 h-2 rounded-full`}
                          style={{ width: `${module.completed}%` }}
                        ></div>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <button className={`flex items-center px-4 py-2 bg-${module.color}-600 text-white rounded-lg hover:bg-${module.color}-700 transition-colors`}>
                        {module.completed === 100 ? (
                          <>
                            <CheckCircle className="h-4 w-4 ml-2" />
                            مراجعة
                          </>
                        ) : (
                          <>
                            <Play className="h-4 w-4 ml-2" />
                            {module.completed > 0 ? 'متابعة' : 'ابدأ'}
                          </>
                        )}
                      </button>
                      
                      <Link 
                        href={`/awareness/module/${module.id}`}
                        className="text-gray-500 hover:text-gray-700"
                      >
                        <ArrowRight className="h-5 w-5" />
                      </Link>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
