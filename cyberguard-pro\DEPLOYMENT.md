# دليل النشر والتشغيل - CyberGuard Pro

## 🚀 خيارات النشر

### 1. النشر على Vercel (الموصى به للتطوير)

```bash
# تثبيت Vercel CLI
npm i -g vercel

# تسجيل الدخول
vercel login

# نشر المشروع
vercel

# نشر للإنتاج
vercel --prod
```

### 2. النشر على Netlify

```bash
# بناء المشروع
npm run build

# رفع مجلد out/ إلى Netlify
# أو ربط المستودع مباشرة
```

### 3. النشر على خادم مخصص

```bash
# بناء المشروع
npm run build

# تشغيل الخادم
npm start

# أو استخدام PM2 للإنتاج
npm install -g pm2
pm2 start npm --name "cyberguard-pro" -- start
```

## 🔧 متطلبات النظام

### الحد الأدنى:
- **المعالج**: 2 CPU cores
- **الذاكرة**: 4GB RAM
- **التخزين**: 20GB SSD
- **النطاق الترددي**: 100Mbps

### الموصى به للإنتاج:
- **المعالج**: 4+ CPU cores
- **الذاكرة**: 8GB+ RAM
- **التخزين**: 100GB+ SSD
- **النطاق الترددي**: 1Gbps
- **قاعدة البيانات**: PostgreSQL أو MongoDB

## 🔐 إعدادات الأمان

### متغيرات البيئة المطلوبة:

```env
# قاعدة البيانات
DATABASE_URL=postgresql://user:password@localhost:5432/cyberguard
MONGODB_URI=mongodb://localhost:27017/cyberguard

# المصادقة
NEXTAUTH_SECRET=your-secret-key-here
NEXTAUTH_URL=https://your-domain.com

# Active Directory
AD_SERVER=ldap://your-ad-server.com
AD_DOMAIN=your-domain.com
AD_USERNAME=service-account
AD_PASSWORD=service-password

# البريد الإلكتروني
SMTP_HOST=smtp.your-provider.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-password

# التخزين السحابي
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=cyberguard-files

# التحليلات
GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX
```

### إعدادات HTTPS:

```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## 📊 مراقبة الأداء

### أدوات المراقبة الموصى بها:

1. **New Relic** - مراقبة الأداء
2. **Sentry** - تتبع الأخطاء
3. **LogRocket** - تسجيل جلسات المستخدمين
4. **Uptime Robot** - مراقبة وقت التشغيل

### مقاييس الأداء المهمة:

- **وقت التحميل**: < 3 ثواني
- **وقت الاستجابة**: < 200ms
- **معدل الأخطاء**: < 1%
- **وقت التشغيل**: > 99.9%

## 🔄 النسخ الاحتياطي والاستعادة

### النسخ الاحتياطي التلقائي:

```bash
#!/bin/bash
# backup-script.sh

# نسخ احتياطي لقاعدة البيانات
pg_dump cyberguard > backup_$(date +%Y%m%d_%H%M%S).sql

# نسخ احتياطي للملفات
tar -czf files_backup_$(date +%Y%m%d_%H%M%S).tar.gz /path/to/uploads

# رفع إلى التخزين السحابي
aws s3 cp backup_*.sql s3://your-backup-bucket/
aws s3 cp files_backup_*.tar.gz s3://your-backup-bucket/
```

### جدولة النسخ الاحتياطي:

```cron
# تشغيل النسخ الاحتياطي يومياً في الساعة 2:00 صباحاً
0 2 * * * /path/to/backup-script.sh
```

## 🔧 الصيانة والتحديثات

### تحديث المشروع:

```bash
# سحب آخر التحديثات
git pull origin main

# تثبيت التبعيات الجديدة
npm install

# بناء المشروع
npm run build

# إعادة تشغيل الخدمة
pm2 restart cyberguard-pro
```

### فحص الأمان:

```bash
# فحص الثغرات الأمنية
npm audit

# إصلاح الثغرات التلقائية
npm audit fix

# فحص التبعيات
npm outdated
```

## 📈 التوسع والأداء

### التوسع الأفقي:

```yaml
# docker-compose.yml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "3000-3002:3000"
    environment:
      - DATABASE_URL=${DATABASE_URL}
    depends_on:
      - db
      - redis
  
  db:
    image: postgres:13
    environment:
      POSTGRES_DB: cyberguard
      POSTGRES_USER: ${DB_USER}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
  
  redis:
    image: redis:6-alpine
    
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - app

volumes:
  postgres_data:
```

### تحسين الأداء:

```javascript
// next.config.js
/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    appDir: true,
  },
  images: {
    domains: ['your-cdn-domain.com'],
    formats: ['image/webp', 'image/avif'],
  },
  compress: true,
  poweredByHeader: false,
  generateEtags: false,
  httpAgentOptions: {
    keepAlive: true,
  },
}

module.exports = nextConfig
```

## 🔍 استكشاف الأخطاء

### مشاكل شائعة وحلولها:

#### 1. بطء في التحميل:
```bash
# فحص استخدام الموارد
htop
df -h
free -m

# تحسين قاعدة البيانات
VACUUM ANALYZE;
REINDEX DATABASE cyberguard;
```

#### 2. أخطاء في قاعدة البيانات:
```bash
# فحص حالة قاعدة البيانات
pg_isready -h localhost -p 5432

# إعادة تشغيل قاعدة البيانات
sudo systemctl restart postgresql
```

#### 3. مشاكل في الذاكرة:
```bash
# زيادة حد الذاكرة لـ Node.js
node --max-old-space-size=4096 server.js

# أو في package.json
"scripts": {
  "start": "node --max-old-space-size=4096 server.js"
}
```

## 📋 قائمة التحقق للنشر

### قبل النشر:
- [ ] اختبار جميع الوظائف
- [ ] فحص الأمان والثغرات
- [ ] تحسين الأداء
- [ ] إعداد النسخ الاحتياطي
- [ ] تكوين مراقبة الأداء
- [ ] اختبار عملية الاستعادة

### بعد النشر:
- [ ] التحقق من عمل جميع الصفحات
- [ ] اختبار تسجيل الدخول
- [ ] فحص سرعة التحميل
- [ ] التأكد من عمل النسخ الاحتياطي
- [ ] مراجعة سجلات الأخطاء
- [ ] اختبار الأمان

## 🆘 الدعم والمساعدة

### معلومات الاتصال:
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +966-XX-XXX-XXXX
- **الدعم الفني**: 24/7

### الموارد المفيدة:
- [توثيق Next.js](https://nextjs.org/docs)
- [دليل Tailwind CSS](https://tailwindcss.com/docs)
- [مرجع TypeScript](https://www.typescriptlang.org/docs)
- [أفضل ممارسات الأمان](https://owasp.org/www-project-top-ten/)

---

**ملاحظة**: يُنصح بمراجعة هذا الدليل بانتظام وتحديثه حسب التطورات والتحديثات الجديدة.
