"use client";

import { useState } from 'react';
import Link from 'next/link';
import { 
  Shield, 
  FileText, 
  CheckCircle, 
  AlertTriangle,
  Clock,
  Award,
  BarChart3,
  Download,
  Eye,
  Edit,
  Plus,
  Filter,
  Search
} from 'lucide-react';

export default function CompliancePage() {
  const [activeTab, setActiveTab] = useState('policies');
  const [selectedFramework, setSelectedFramework] = useState('all');

  const complianceFrameworks = [
    {
      id: 'nca',
      name: 'NCA ECC',
      fullName: 'الضوابط الأساسية للأمن السيبراني',
      progress: 87,
      total: 114,
      completed: 99,
      color: 'blue'
    },
    {
      id: 'iso27001',
      name: 'ISO 27001',
      fullName: 'نظام إدارة أمن المعلومات',
      progress: 92,
      total: 93,
      completed: 86,
      color: 'green'
    },
    {
      id: 'nist',
      name: 'NIST Framework',
      fullName: 'إطار عمل الأمن السيبراني',
      progress: 78,
      total: 108,
      completed: 84,
      color: 'purple'
    }
  ];

  const policies = [
    {
      id: 1,
      title: 'سياسة كلمات المرور',
      category: 'الوصول والهوية',
      status: 'approved',
      lastUpdated: '2024-01-15',
      readBy: 245,
      totalEmployees: 280,
      framework: ['nca', 'iso27001'],
      version: '2.1'
    },
    {
      id: 2,
      title: 'سياسة استخدام البريد الإلكتروني',
      category: 'الاتصالات',
      status: 'review',
      lastUpdated: '2024-01-10',
      readBy: 198,
      totalEmployees: 280,
      framework: ['nca', 'nist'],
      version: '1.8'
    },
    {
      id: 3,
      title: 'سياسة النسخ الاحتياطي',
      category: 'حماية البيانات',
      status: 'draft',
      lastUpdated: '2024-01-08',
      readBy: 0,
      totalEmployees: 280,
      framework: ['iso27001', 'nist'],
      version: '3.0'
    },
    {
      id: 4,
      title: 'سياسة الوصول عن بُعد',
      category: 'الوصول والهوية',
      status: 'approved',
      lastUpdated: '2024-01-12',
      readBy: 267,
      totalEmployees: 280,
      framework: ['nca', 'iso27001', 'nist'],
      version: '1.5'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'green';
      case 'review': return 'yellow';
      case 'draft': return 'gray';
      default: return 'gray';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'approved': return 'معتمدة';
      case 'review': return 'قيد المراجعة';
      case 'draft': return 'مسودة';
      default: return 'غير محدد';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/dashboard" className="flex items-center">
                <Shield className="h-8 w-8 text-blue-600 ml-2" />
                <h1 className="text-2xl font-bold text-gray-900 mr-3">CyberGuard Pro</h1>
              </Link>
            </div>
            
            <nav className="flex items-center space-x-6">
              <Link href="/dashboard" className="text-gray-600 hover:text-gray-900">
                لوحة التحكم
              </Link>
              <Link href="/awareness" className="text-gray-600 hover:text-gray-900">
                الثقافة الأمنية
              </Link>
              <Link href="/compliance" className="text-blue-600 font-medium">
                الامتثال
              </Link>
            </nav>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-2">
            وحدة الامتثال والجاهزية
          </h2>
          <p className="text-lg text-gray-600">
            إدارة السياسات ومتابعة الامتثال للمعايير المحلية والعالمية
          </p>
        </div>

        {/* Compliance Frameworks Overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          {complianceFrameworks.map((framework) => (
            <div key={framework.id} className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h3 className="text-lg font-medium text-gray-900">
                    {framework.name}
                  </h3>
                  <p className="text-sm text-gray-600">
                    {framework.fullName}
                  </p>
                </div>
                <Award className={`h-8 w-8 text-${framework.color}-600`} />
              </div>
              
              <div className="mb-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium text-gray-700">
                    التقدم العام
                  </span>
                  <span className="text-sm text-gray-500">
                    {framework.completed}/{framework.total}
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className={`bg-${framework.color}-600 h-2 rounded-full`}
                    style={{ width: `${framework.progress}%` }}
                  ></div>
                </div>
              </div>
              
              <div className="flex justify-between items-center">
                <span className={`text-2xl font-bold text-${framework.color}-600`}>
                  {framework.progress}%
                </span>
                <button className={`text-${framework.color}-600 hover:text-${framework.color}-700`}>
                  <BarChart3 className="h-5 w-5" />
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Navigation Tabs */}
        <div className="mb-8">
          <nav className="flex space-x-8">
            {[
              { id: 'policies', label: 'السياسات', icon: FileText },
              { id: 'controls', label: 'الضوابط', icon: Shield },
              { id: 'assessments', label: 'التقييمات', icon: CheckCircle },
              { id: 'reports', label: 'التقارير', icon: BarChart3 }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                  activeTab === tab.id
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
                }`}
              >
                <tab.icon className="h-5 w-5 ml-2" />
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Policies Tab Content */}
        {activeTab === 'policies' && (
          <div className="bg-white rounded-lg shadow">
            {/* Policies Header */}
            <div className="p-6 border-b border-gray-200">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium text-gray-900">
                  إدارة السياسات
                </h3>
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <Filter className="h-5 w-5 text-gray-400" />
                    <select 
                      value={selectedFramework}
                      onChange={(e) => setSelectedFramework(e.target.value)}
                      className="border border-gray-300 rounded-lg px-3 py-1 text-sm"
                    >
                      <option value="all">جميع المعايير</option>
                      <option value="nca">NCA ECC</option>
                      <option value="iso27001">ISO 27001</option>
                      <option value="nist">NIST Framework</option>
                    </select>
                  </div>
                  <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center">
                    <Plus className="h-4 w-4 ml-2" />
                    سياسة جديدة
                  </button>
                </div>
              </div>
            </div>

            {/* Policies List */}
            <div className="p-6">
              <div className="space-y-4">
                {policies.map((policy) => (
                  <div key={policy.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center mb-2">
                          <h4 className="text-lg font-medium text-gray-900 ml-3">
                            {policy.title}
                          </h4>
                          <span className={`px-2 py-1 text-xs font-medium rounded-full bg-${getStatusColor(policy.status)}-100 text-${getStatusColor(policy.status)}-800`}>
                            {getStatusText(policy.status)}
                          </span>
                        </div>
                        
                        <div className="flex items-center text-sm text-gray-600 mb-2">
                          <span className="ml-4">{policy.category}</span>
                          <span className="mx-2">•</span>
                          <span className="ml-4">الإصدار {policy.version}</span>
                          <span className="mx-2">•</span>
                          <span>آخر تحديث: {policy.lastUpdated}</span>
                        </div>
                        
                        <div className="flex items-center space-x-4">
                          <div className="flex items-center">
                            <span className="text-sm text-gray-600">
                              قرأها {policy.readBy} من {policy.totalEmployees} موظف
                            </span>
                            <div className="w-24 bg-gray-200 rounded-full h-2 mr-2">
                              <div 
                                className="bg-green-600 h-2 rounded-full"
                                style={{ width: `${(policy.readBy / policy.totalEmployees) * 100}%` }}
                              ></div>
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex items-center mt-2">
                          <span className="text-sm text-gray-600 ml-2">المعايير:</span>
                          {policy.framework.map((fw) => (
                            <span key={fw} className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded mr-1">
                              {fw.toUpperCase()}
                            </span>
                          ))}
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <button className="p-2 text-gray-400 hover:text-gray-600">
                          <Eye className="h-5 w-5" />
                        </button>
                        <button className="p-2 text-gray-400 hover:text-gray-600">
                          <Edit className="h-5 w-5" />
                        </button>
                        <button className="p-2 text-gray-400 hover:text-gray-600">
                          <Download className="h-5 w-5" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Controls Tab Content */}
        {activeTab === 'controls' && (
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              الضوابط الأمنية
            </h3>
            <p className="text-gray-600">
              عرض وإدارة الضوابط الأمنية المطلوبة حسب كل معيار...
            </p>
          </div>
        )}

        {/* Assessments Tab Content */}
        {activeTab === 'assessments' && (
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              التقييمات
            </h3>
            <p className="text-gray-600">
              إجراء وإدارة تقييمات الامتثال والمراجعات الداخلية...
            </p>
          </div>
        )}

        {/* Reports Tab Content */}
        {activeTab === 'reports' && (
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              التقارير
            </h3>
            <p className="text-gray-600">
              إنشاء تقارير شاملة للمراجعين والمدققين الداخليين والخارجيين...
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
