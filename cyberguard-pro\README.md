# CyberGuard Pro - منصة الأمن السيبراني المتكاملة

منصة شاملة لتعزيز الثقافة الأمنية لدى الموظفين ورفع الجاهزية للتدقيق والامتثال بالمعايير المحلية والعالمية مثل NCA و ISO 27001 و NIST.

## 🎯 الهدف من المنصة

تهدف منصة CyberGuard Pro إلى الجمع بين جانبين أساسيين:

1. **تعزيز الثقافة الأمنية لدى الموظفين** - من خلال محتوى تفاعلي ونظام مكافآت
2. **رفع الجاهزية للتدقيق والامتثال** - للمعايير المحلية والعالمية

## 🏗️ هيكل المنصة

### وحدة الثقافة الأمنية (Human-Centric Cyber Awareness)

- **محتوى تفاعلي مميز**: فيديوهات، تحديات، سيناريوهات حية، رسائل تصيّد وهمية
- **اختبارات معرفية ذكية**: تتغير تلقائياً حسب مستوى المستخدم
- **تقارير شخصية**: توضح مستوى وعي كل موظف
- **لوحة تحكم للإدارة**: توضح مستوى التفاعل ونقاط الضعف
- **نظام مكافآت**: شارات، تصنيفات، وتحفيز

### وحدة الامتثال والجاهزية (Governance & Compliance Readiness)

- **نظام إدارة السياسات الأمنية**: مع تتبع من قرأ ووافق على كل سياسة
- **مكتبة توثيق داخلي**: تشمل الإجراءات، العمليات، والأدلة
- **مؤشرات امتثال فورية**: مقابل كل معيار (NCA ECC، ISO 27001، NIST)
- **نظام متابعة الإجراءات التصحيحية**
- **تقارير جاهزة**: للمراجعين والمدققين الداخليين والخارجيين

## 🛠️ التقنيات المستخدمة

- **Frontend**: Next.js 15 مع TypeScript
- **Styling**: Tailwind CSS
- **Icons**: Lucide React
- **UI Components**: مكونات مخصصة
- **Language**: دعم كامل للغة العربية (RTL)

## 📁 هيكل المشروع

```
cyberguard-pro/
├── src/
│   ├── app/
│   │   ├── page.tsx                 # الصفحة الرئيسية
│   │   ├── dashboard/
│   │   │   └── page.tsx            # لوحة التحكم الرئيسية
│   │   ├── awareness/
│   │   │   └── page.tsx            # وحدة الثقافة الأمنية
│   │   ├── compliance/
│   │   │   └── page.tsx            # وحدة الامتثال والجاهزية
│   │   ├── layout.tsx              # التخطيط الأساسي
│   │   └── globals.css             # الأنماط العامة
│   ├── components/
│   │   └── ui/
│   │       └── Button.tsx          # مكون الأزرار
│   └── lib/
│       └── utils.ts                # الوظائف المساعدة
├── public/                         # الملفات العامة
├── package.json
└── README.md
```

## 🚀 البدء

### المتطلبات

- Node.js 18+
- npm أو yarn أو pnpm

### التثبيت

```bash
# استنساخ المشروع
git clone [repository-url]
cd cyberguard-pro

# تثبيت التبعيات
npm install

# تشغيل الخادم المحلي
npm run dev
```

افتح [http://localhost:3000](http://localhost:3000) في المتصفح لرؤية المنصة.

## 📊 الصفحات والوحدات

### 1. الصفحة الرئيسية (`/`)
- عرض تقديمي للمنصة
- نظرة عامة على الوحدات
- المعايير المدعومة
- روابط التنقل

### 2. لوحة التحكم (`/dashboard`)
- إحصائيات عامة
- مؤشرات الأداء الرئيسية
- الأنشطة الأخيرة
- إجراءات سريعة

### 3. وحدة الثقافة الأمنية (`/awareness`)
- دورات تدريبية تفاعلية
- نظام الإنجازات والشارات
- تصنيف الدورات حسب الفئة
- تتبع التقدم

### 4. وحدة الامتثال (`/compliance`)
- إدارة السياسات
- مؤشرات الامتثال للمعايير
- الضوابط الأمنية
- التقييمات والتقارير

## 🎨 التصميم والواجهة

### الألوان الأساسية
- **الأزرق**: `blue-600` - للعناصر الأساسية
- **الأخضر**: `green-600` - للحالات الإيجابية
- **الأحمر**: `red-600` - للتحذيرات
- **البنفسجي**: `purple-600` - للتحليلات
- **البرتقالي**: `orange-600` - للإشعارات

### المكونات الأساسية
- أزرار تفاعلية مع حالات مختلفة
- بطاقات معلومات مع أيقونات
- أشرطة تقدم ملونة
- نظام شبكة مرن

## 🔧 المميزات التقنية

### دعم اللغة العربية
- اتجاه النص من اليمين إلى اليسار (RTL)
- خطوط محسنة للعربية
- تخطيط متجاوب مع النصوص العربية

### التجاوب
- تصميم متجاوب بالكامل
- دعم جميع أحجام الشاشات
- تحسين للأجهزة المحمولة

### الأداء
- تحميل سريع مع Next.js
- تحسين الصور والخطوط
- كود مقسم ومحسن

## 📈 خطة التطوير التدريجية

### المرحلة الأولى (MVP)
- [x] الصفحة الرئيسية
- [x] لوحة التحكم الأساسية
- [x] واجهة وحدة الثقافة الأمنية
- [x] واجهة وحدة الامتثال
- [x] التصميم الأساسي والتنقل

### المرحلة الثانية
- [ ] نظام المصادقة والتفويض
- [ ] قاعدة البيانات والـ API
- [ ] نظام إدارة المحتوى
- [ ] تتبع التقدم الفعلي

### المرحلة الثالثة
- [ ] نظام التقييمات التفاعلية
- [ ] محاكاة رسائل التصيد
- [ ] تقارير متقدمة
- [ ] تكامل مع Active Directory

### المرحلة الرابعة
- [ ] الذكاء الاصطناعي للتوصيات
- [ ] تحليلات متقدمة
- [ ] تطبيق الهاتف المحمول
- [ ] تكامل مع أنظمة خارجية

## 🎯 أمثلة على المحتوى

### أسئلة الثقافة الأمنية

#### أساسيات الأمن السيبراني
1. ما هي أفضل طريقة لإنشاء كلمة مرور قوية؟
2. كيف يمكن التعرف على رسائل التصيد الإلكتروني؟
3. ما هي أهمية تحديث البرامج بانتظام؟

#### التصيد الإلكتروني
1. ما هي العلامات التحذيرية لرسائل التصيد؟
2. كيف تتعامل مع رابط مشبوه في البريد الإلكتروني؟
3. ما الفرق بين التصيد والتصيد المستهدف؟

#### أمان كلمات المرور
1. ما هي خصائص كلمة المرور القوية؟
2. لماذا يجب عدم إعادة استخدام كلمات المرور؟
3. ما هي فوائد استخدام مدير كلمات المرور؟

## 🏆 نظام المكافآت والإنجازات

### الشارات
- **خبير الأمان**: إكمال 10 دورات تدريبية
- **صياد التصيد**: اجتياز جميع اختبارات التصيد
- **حارس كلمات المرور**: تحقيق 100% في اختبار كلمات المرور
- **المتعلم المستمر**: التدرب لمدة 30 يوم متتالي

### نظام النقاط
- إكمال دورة تدريبية: 100 نقطة
- اجتياز اختبار: 50 نقطة
- تقرير حادثة أمنية: 200 نقطة
- مشاركة المحتوى: 25 نقطة

## 📋 المعايير المدعومة

### NCA ECC (الضوابط الأساسية للأمن السيبراني)
- 114 ضابط أساسي
- تصنيف حسب المجالات
- مؤشرات امتثال فورية

### ISO 27001
- 93 ضابط أمني
- نظام إدارة أمن المعلومات
- تقييمات دورية

### NIST Cybersecurity Framework
- 108 ضابط
- 5 وظائف أساسية
- إطار عمل شامل

## 🤝 المساهمة

نرحب بالمساهمات! يرجى قراءة دليل المساهمة قبل البدء.

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## 📞 التواصل

للاستفسارات والدعم الفني، يرجى التواصل معنا عبر:
- البريد الإلكتروني: <EMAIL>
- الموقع الإلكتروني: www.cyberguard-pro.com

---

**CyberGuard Pro** - حماية مؤسستك تبدأ من هنا 🛡️
